<?php
// Callback handler for payment processing

/**
 * Function to connect to Zibal API for verification
 */
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response);
}

// بررسی پارامترهای ضروری
if (isset($_GET['trackId']) && isset($_GET['orderId'])) {
    $trackId = $_GET['trackId'];
    $orderId = $_GET['orderId'];
    $success = $_GET['success'] ?? '0';
    $status = $_GET['status'] ?? '0';

    echo "<h3>در حال بررسی پرداخت...</h3>";
    echo "<p>شناسه پیگیری: $trackId</p>";
    echo "<p>شماره سفارش: $orderId</p>";

    // تأیید پرداخت با API درگاه Zibal
    $parameters = array(
        "merchant" => "zibal", // کلید مرچنت واقعی خود را قرار دهید
        "trackId" => $trackId
    );

    $response = postToZibal('verify', $parameters);

    if ($response) {
        $resultCode = $response->result ?? 'undefined';

        // بررسی کد 203 (تراکنش قبلاً تأیید شده)
        if ($resultCode == 203) {
            $message = $response->message ?? '';
            // اگر پیام "تایید شده" باشد، موفق در نظر بگیر
            if (strpos($message, 'تایید شده') !== false || strpos($message, 'verified') !== false) {
                $response->result = 100;
            }
        }

        // بررسی نتیجه تأیید (100 = موفق)
        if (isset($response->result) && $response->result == 100) {
            // پرداخت موفق تأیید شد
            $voucher_code = 'BV-' . strtoupper(substr(md5(time() . rand()), 0, 8));
            $amount = ($response->amount ?? 0) / 10; // تبدیل ریال به تومان

            echo "<h2 style='color: green;'>✅ پرداخت موفق</h2>";
            echo "<p><strong>کد ووچر شما: $voucher_code</strong></p>";
            echo "<p>مبلغ پرداختی: " . number_format($amount) . " تومان</p>";
            echo "<p>تاریخ: " . date('Y/m/d H:i:s') . "</p>";
            echo "<p>لطفاً این کد را یادداشت کنید.</p>";

        } else {
            // پرداخت ناموفق
            echo "<h2 style='color: red;'>❌ پرداخت ناموفق</h2>";
            echo "<p>متأسفانه پرداخت شما تأیید نشد.</p>";
            echo "<p>کد خطا: $resultCode</p>";
            echo "<p>در صورت کسر وجه، مبلغ ظرف 72 ساعت بازگردانده می‌شود.</p>";
        }
    } else {
        // خطا در ارتباط با API
        echo "<h2 style='color: orange;'>⚠️ خطا در بررسی پرداخت</h2>";
        echo "<p>امکان بررسی وضعیت پرداخت وجود ندارد.</p>";
        echo "<p>لطفاً با پشتیبانی تماس بگیرید.</p>";
    }

} else {
    // پارامترهای ضروری موجود نیست
    echo "<h2 style='color: red;'>❌ خطا</h2>";
    echo "<p>اطلاعات پرداخت ناقص است.</p>";
    echo "<p>Debug: " . json_encode($_GET) . "</p>";
}
?>
