<?php
// Callback handler for payment processing

// تنظیمات ربات تلگرام
$botToken = "7626326794:AAHKrt-VqqMbv72cJzbLIFMGQK-nE647fiE";

/**
 * Function to send Telegram message
 */
function sendTelegramMessage($chat_id, $text, $reply_markup = null) {
    global $botToken;
    $url = "https://api.telegram.org/bot{$botToken}/sendMessage";

    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response);
}

/**
 * Function to connect to Zibal API for verification
 */
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response);
}

// بررسی پارامترهای ضروری
if (isset($_GET['trackId']) && isset($_GET['orderId'])) {
    $trackId = $_GET['trackId'];
    $orderId = $_GET['orderId'];
    $success = $_GET['success'] ?? '0';
    $status = $_GET['status'] ?? '0';

    // استخراج user_id از orderId (فرمت: BV-TIMESTAMP-USERID یا SPX-TIMESTAMP-USERID)
    $user_id = '0';
    if (preg_match('/BV-\d+-(\d+)/', $orderId, $matches)) {
        $user_id = $matches[1];
    } else if (preg_match('/SPX-\d+-(\d+)/', $orderId, $matches)) {
        $user_id = $matches[1];
    } else if (preg_match('/(\d+)$/', $orderId, $matches)) {
        // Fallback to old format
        $user_id = $matches[1];
    }

    echo "<h3>در حال بررسی پرداخت...</h3>";
    echo "<p>شناسه پیگیری: $trackId</p>";
    echo "<p>شماره سفارش: $orderId</p>";
    echo "<p>کاربر: $user_id</p>";

    // تأیید پرداخت با API درگاه Zibal
    $parameters = array(
        "merchant" => "zibal", // کلید مرچنت واقعی خود را قرار دهید
        "trackId" => $trackId
    );

    $response = postToZibal('verify', $parameters);

    if ($response) {
        $resultCode = $response->result ?? 'undefined';

        // بررسی کد 203 (تراکنش قبلاً تأیید شده)
        if ($resultCode == 203) {
            $message = $response->message ?? '';
            // اگر پیام "تایید شده" باشد، موفق در نظر بگیر
            if (strpos($message, 'تایید شده') !== false || strpos($message, 'verified') !== false) {
                $response->result = 100;
            }
        }

        // بررسی نتیجه تأیید (100 = موفق)
        if (isset($response->result) && $response->result == 100) {
            // پرداخت موفق تأیید شد
            $voucher_code = 'BV-' . strtoupper(substr(md5(time() . rand()), 0, 8));
            $amount = ($response->amount ?? 0) / 10; // تبدیل ریال به تومان

            // ذخیره کد ووچر در session یا فایل برای نمایش در صفحه success
            session_start();
            $_SESSION['voucher_code'] = $voucher_code;
            $_SESSION['payment_amount'] = $amount;
            $_SESSION['payment_date'] = date('Y/m/d H:i:s');

            // Redirect به صفحه موفقیت
            header('Location: https://speedx-team.ir/Payment/success.php');
            exit;

        } else {
            // پرداخت ناموفق - ارسال پیام به کاربر
            if ($user_id && $user_id != '0') {
                $reply_markup = json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => '🔄 تلاش مجدد', 'callback_data' => 'buy_voucher']
                        ]
                    ]
                ]);

                sendTelegramMessage($user_id,
                    "❌ پرداخت ناموفق\n\nمتأسفانه پرداخت شما تأیید نشد.\n\n✱ کد خطا: <code>$resultCode</code>\n✱ در صورت کسر وجه از حساب شما، مبلغ ظرف 72 ساعت بازگردانده خواهد شد.\n\n🔄 برای تلاش مجدد، دکمه زیر را بزنید.",
                    $reply_markup
                );
            }

            // Redirect به صفحه خطا با کد خطا
            header('Location: https://speedx-team.ir/Payment/error.php?error=verification_failed&code=' . $resultCode);
            exit;
        }
    } else {
        // خطا در ارتباط با API
        if ($user_id && $user_id != '0') {
            sendTelegramMessage($user_id,
                "❌ خطا در ارتباط با درگاه پرداخت!\n\nمتاسفانه امکان بررسی وضعیت پرداخت شما وجود ندارد. اگر مبلغی از حساب شما کسر شده، لطفا با پشتیبانی تماس بگیرید."
            );
        }

        header('Location: https://speedx-team.ir/Payment/error.php?error=api_error');
        exit;
    }

} else {
    // پارامترهای ضروری موجود نیست
    // سعی در استخراج user_id از orderId اگر موجود باشد
    if (isset($_GET['orderId'])) {
        $orderId = $_GET['orderId'];
        $user_id = '0';

        if (preg_match('/BV-\d+-(\d+)/', $orderId, $matches)) {
            $user_id = $matches[1];
        } else if (preg_match('/SPX-\d+-(\d+)/', $orderId, $matches)) {
            $user_id = $matches[1];
        } else if (preg_match('/(\d+)$/', $orderId, $matches)) {
            $user_id = $matches[1];
        }

        if ($user_id && $user_id != '0') {
            $reply_markup = json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => '🔄 تلاش مجدد', 'callback_data' => 'buy_voucher']
                    ]
                ]
            ]);

            sendTelegramMessage($user_id,
                "❌ پرداخت ناموفق!\n\nمتاسفانه پرداخت شما با موفقیت انجام نشد. می‌توانید مجدداً تلاش کنید یا با پشتیبانی تماس بگیرید.",
                $reply_markup
            );
        }
    }

    header('Location: https://speedx-team.ir/Payment/error.php?error=missing_parameters');
    exit;
}
?>
