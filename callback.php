<?php
// Callback handler for payment processing

// Check if payment was successful
// Only show success if BOTH success=1 AND status=2
if (isset($_GET['success']) && isset($_GET['status']) && 
    $_GET['success'] == '1' && $_GET['status'] == '2') {
    
    // Generate a simple voucher code
    $voucher_code = 'BV-' . strtoupper(substr(md5(time() . rand()), 0, 8));
    
    echo "<h2>پرداخت موفق</h2>";
    echo "<p>کد ووچر شما: <strong>" . $voucher_code . "</strong></p>";
    echo "<p>لطفاً این کد را یادداشت کنید.</p>";
    
} else {
    echo "<h2>پرداخت ناموفق</h2>";
    echo "<p>متأسفانه پرداخت شما انجام نشد.</p>";
    
    // Debug info (remove this in production)
    echo "<p>Debug: success=" . ($_GET['success'] ?? 'not set') . ", status=" . ($_GET['status'] ?? 'not set') . "</p>";
}
?>
